import { Suspense } from 'react'
import { Metadata } from 'next'
import ExamClient from './exam-client'
import { getLessonServerSide, getSubjectServerSide } from '@/backend/api/educationAPI'
import { generateExamMetadata } from '@/lib/seo'

type Props = {
  params: { lessonId: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const lesson = await getLessonServerSide(params.lessonId)
    if (!lesson) {
      return {
        title: 'الامتحان غير موجود - منصة التعليم العربي',
        description: 'لم يتم العثور على الدرس المطلوب',
        robots: { index: false, follow: false }
      }
    }

    // فحص إذا كان الامتحان فارغ (بدون أسئلة)
    if (!lesson.exercises || lesson.exercises.length === 0) {
      return {
        title: `امتحان ${lesson.title} - قيد التطوير`,
        description: 'هذا الامتحان قيد التطوير وسيتم إضافة الأسئلة قريباً',
        robots: { index: false, follow: false }
      }
    }

    const subject = await getSubjectServerSide(lesson.subjectId)
    return generateExamMetadata(lesson, subject)
  } catch (error) {
    console.error('Error generating exam metadata:', error)
    return {
      title: 'الامتحان - منصة التعليم العربي',
      description: 'استكشف أسئلة الامتحان',
      robots: { index: false, follow: false }
    }
  }
}

// Loading component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
)

export default function ExamPage({ params }: Props) {
  return (
    <Suspense fallback={<PageLoader />}>
      <ExamClient lessonId={params.lessonId} />
    </Suspense>
  )
}
