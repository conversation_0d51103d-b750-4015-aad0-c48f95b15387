import { Suspense } from 'react'
import { Metadata } from 'next'
import SummaryClient from './summary-client'
import { getLessonServerSide, getSubjectServerSide } from '@/backend/api/educationAPI'
import { generateSummaryMetadata } from '@/lib/seo'

type Props = {
  params: { lessonId: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const lesson = await getLessonServerSide(params.lessonId)
    if (!lesson) {
      return {
        title: 'الملخص غير موجود - منصة التعليم العربي',
        description: 'لم يتم العثور على الدرس المطلوب',
        robots: { index: false, follow: false }
      }
    }

    // فحص إذا كان الملخص فارغ (بدون محتوى)
    if (!lesson.exercises || lesson.exercises.length === 0) {
      return {
        title: `ملخص ${lesson.title} - قيد التطوير`,
        description: 'هذا الملخص قيد التطوير وسيتم إضافة المحتوى قريباً',
        robots: { index: false, follow: false }
      }
    }

    const subject = await getSubjectServerSide(lesson.subjectId)
    return generateSummaryMetadata(lesson, subject)
  } catch (error) {
    console.error('Error generating summary metadata:', error)
    return {
      title: 'الملخص - منصة التعليم العربي',
      description: 'استكشف ملخص الدرس',
      robots: { index: false, follow: false }
    }
  }
}

// Loading component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
)

export default function SummaryPage({ params }: Props) {
  return (
    <Suspense fallback={<PageLoader />}>
      <SummaryClient lessonId={params.lessonId} />
    </Suspense>
  )
}
