import { Suspense } from 'react'
import { Metadata } from 'next'
import HomeworkClient from './homework-client'
import { getLessonServerSide, getSubjectServerSide } from '@/backend/api/educationAPI'
import { generateHomeworkMetadata } from '@/lib/seo'

type Props = {
  params: { lessonId: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const lesson = await getLessonServerSide(params.lessonId)
    if (!lesson) {
      return {
        title: 'الفرض المنزلي غير موجود - منصة التعليم العربي',
        description: 'لم يتم العثور على الدرس المطلوب',
        robots: { index: false, follow: false }
      }
    }

    // فحص إذا كان الفرض فارغ (بدون تمارين)
    if (!lesson.exercises || lesson.exercises.length === 0) {
      return {
        title: `فرض ${lesson.title} - قيد التطوير`,
        description: 'هذا الفرض قيد التطوير وسيتم إضافة المحتوى قريباً',
        robots: { index: false, follow: false }
      }
    }

    const subject = await getSubjectServerSide(lesson.subjectId)
    return generateHomeworkMetadata(lesson, subject)
  } catch (error) {
    console.error('Error generating homework metadata:', error)
    return {
      title: 'الفرض المنزلي - منصة التعليم العربي',
      description: 'استكشف تمارين الفرض المنزلي',
      robots: { index: false, follow: false }
    }
  }
}

// Loading component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
)

export default function HomeworkPage({ params }: Props) {
  return (
    <Suspense fallback={<PageLoader />}>
      <HomeworkClient lessonId={params.lessonId} />
    </Suspense>
  )
}
